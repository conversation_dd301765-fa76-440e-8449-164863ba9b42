<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { GetTenantPackageList, ManualAddPayRecord } from '@/api'
import { formatPriceMul } from '@/common/format'
import UploadFile from '@/components/UploadFile/index.vue'

const emits = defineEmits(['handleSubmit'])

const { fetchData: getTenantPackageList, data: tenantPackageList } = GetTenantPackageList()

const { fetchData: handAddFun, success: addSuccess, msg: addMessage, loading: addLoading } = ManualAddPayRecord()

onMounted(async () => {
  await getTenantPackageList()
})

// 表单引用
const formRef = ref()

const state = reactive({
  showModal: false,
  modalName: '添加购买记录',
  form: {
    tenant_management_id: '',
    // 商户订单号
    trade_no: '',
    // 微信支付订单号
    pay_record_order_no: '',
    // 支付者昵称
    payer_name: '',
    // 支付时间
    pay_time: '',
    // 支付金额
    pay_price: '',
    // 支付内容
    tenant_package_id: '',
    // 服务天数
    start_time: '',
    // 支付凭证
    pay_voucher_url: '',
    // 支付凭证文件列表（用于上传组件）
    payment_vouchers: [] as string[],
  },
})

// 表单验证规则
const formRules = {
  pay_price: [
    { required: true, message: '请输入支付金额', trigger: 'blur' },
    {
      pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
      message: '请输入正确的金额格式',
      trigger: 'blur',
    },
  ],
  tenant_package_id: [
    { required: true, message: '请选择支付内容', trigger: 'change' },
  ],
  start_time: [
    { required: true, message: '请选择服务开始时间', trigger: 'change' },
  ],
  payment_vouchers: [
    {
      required: true,
      validator: (_rule: any, value: any, callback: any) => {
        if (!value || value.length === 0)
          callback(new Error('请上传支付凭证'))
        else
          callback()
      },
      trigger: 'change',
    },
  ],
}

defineExpose({
  state,
})

async function handUpload(url: any) {
  state.form.pay_voucher_url = url[0]
  state.form.payment_vouchers = url
  // 触发表单验证
  formRef.value?.validateField('payment_vouchers')
}

// async function handleUploadFile(rawFile: File) {
//   return new Promise<{ flag: boolean, msg?: string, res?: any }>(async (resolve, reject) => {
//     if (rawFile) {
//       const res: any = await uploadCDNImg(rawFile)
//
//       if (res?.code == 200)
//         resolve({ flag: true, res })
//       else
//         reject({ flag: false, msg: '上传失败' })
//     }
//     else {
//       reject({ flag: false, msg: '上传失败' })
//     }
//   })
// }

async function handleSubmit() {
  // 使用表单验证
  if (!formRef.value)
    return

  try {
    await formRef.value.validate()
  }
  catch (error) {
    ElMessage.error('请检查表单填写是否正确')
    return
  }

  const query = {
    tenant_management_id: Number(state.form.tenant_management_id),
    trade_no: state.form.trade_no,
    pay_record_order_no: state.form.pay_record_order_no,
    payer_name: state.form.payer_name,
    pay_time: state.form.pay_time,
    pay_price: formatPriceMul(Number(state.form.pay_price)),
    tenant_package_id: state.form.tenant_package_id,
    start_time: state.form.start_time,
    pay_voucher_url: state.form.pay_voucher_url,
  }

  await handAddFun(query)

  if (!addSuccess.value) {
    ElMessage.error(addMessage.value)
    return
  }

  emits('handleSubmit')

  state.showModal = false
  // 重置表单
  resetForm()
}

// 重置表单
function resetForm() {
  state.form = {
    tenant_management_id: '',
    trade_no: '',
    pay_record_order_no: '',
    payer_name: '',
    pay_time: '',
    pay_price: '',
    tenant_package_id: '',
    start_time: '',
    pay_voucher_url: '',
    payment_vouchers: [],
  }
  // 清除表单验证状态
  formRef.value?.clearValidate()
}

const finalValidityPeriod = computed(() => {
  const period_of_validity_days = tenantPackageList.value.list.find((item: any) => item.id === state.form.tenant_package_id)?.period_of_validity_days
  if (period_of_validity_days && state.form.start_time) {
    const endDate = new Date(new Date(state.form.start_time).getTime() + period_of_validity_days * 24 * 60 * 60 * 1000)

    const year = endDate.getFullYear()
    const month = (endDate.getMonth() + 1).toString().padStart(2, '0') // 月从0开始，加1，并确保两位数字
    const day = endDate.getDate().toString().padStart(2, '0') // 确保天是两位数字

    return `${year}-${month}-${day}` // 使用模板字符串格式化日期
  }
  return ''
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="720" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <el-form
      ref="formRef"
      :model="state.form"
      :rules="formRules"
      label-width="120px"
      label-position="left"
      class="form-container"
    >
      <div class="form-grid">
        <el-form-item label="商户订单号" prop="trade_no">
          <el-input
            v-model="state.form.trade_no"
            placeholder="请输入商户订单号"
            clearable
          />
        </el-form-item>

        <el-form-item label="微信支付订单号" prop="pay_record_order_no">
          <el-input
            v-model="state.form.pay_record_order_no"
            placeholder="请输入微信支付订单号"
            clearable
          />
        </el-form-item>

        <el-form-item label="支付者昵称" prop="payer_name">
          <el-input
            v-model="state.form.payer_name"
            placeholder="请输入支付者昵称"
            clearable
          />
        </el-form-item>

        <el-form-item label="支付时间" prop="pay_time">
          <el-date-picker
            v-model="state.form.pay_time"
            type="date"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            placeholder="请选择支付时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="支付金额" prop="pay_price" required>
          <el-input
            v-model="state.form.pay_price"
            type="number"
            placeholder="请输入支付金额"
            clearable
          >
            <template #append>
              元
            </template>
          </el-input>
        </el-form-item>

        <el-form-item label="支付内容" prop="tenant_package_id" required>
          <el-select
            v-model="state.form.tenant_package_id"
            placeholder="请选择购买服务的内容"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in tenantPackageList.list"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="服务开始时间" prop="start_time" required>
          <el-date-picker
            v-model="state.form.start_time"
            format="YYYY/MM/DD"
            value-format="YYYY-MM-DD"
            type="date"
            placeholder="请选择服务开始时间"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="服务结束时间" class="readonly-item">
          <el-input
            :value="finalValidityPeriod"
            readonly
            placeholder="根据开始时间自动计算"
          />
        </el-form-item>

        <el-form-item label="支付凭证" prop="payment_vouchers" required class="upload-item">
          <UploadFile
            v-model:file-list="state.form.payment_vouchers"
            :multiple="true"
            :show-submit-btn="false"
            :auto-upload="true"
            :image-shown="true"
            scene="payment"
            @on-upload-success="handUpload"
          />
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <!--      <el-button @click="handCancel">取消</el-button> -->
      <el-button type="primary" :loading="addLoading" @click="handleSubmit">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.form-container {
  padding: 20px 0;

  .form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px 30px;

    .el-form-item {
      margin-bottom: 0;

      :deep(.el-form-item__label) {
        font-weight: 500;
        color: #333;
      }

      :deep(.el-form-item__content) {
        flex: 1;
      }
    }

    // 上传组件占满整行
    .upload-item {
      grid-column: 1 / -1;

      .form-tip {
        margin-top: 8px;
        font-size: 12px;
        color: #999;
        line-height: 1.4;
      }
    }

    // 只读项样式
    .readonly-item {
      :deep(.el-input__inner) {
        background-color: #f5f7fa;
        color: #909399;
      }
    }
  }

  // 响应式布局
  @media (max-width: 768px) {
    .form-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}

// 必填字段标识
:deep(.el-form-item.is-required .el-form-item__label::before) {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
</style>
