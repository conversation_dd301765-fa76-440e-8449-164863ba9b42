# UploadFile 组件

一个功能完整的文件上传组件，支持多种文件类型、实时进度显示、拖拽上传、图片裁剪等功能。

## 🚀 功能特性

- ✅ **多文件类型支持**：图片、文档、压缩包等
- ✅ **实时进度显示**：上传进度条和状态反馈
- ✅ **智能文件图标**：根据文件类型显示对应图标
- ✅ **拖拽上传**：支持拖拽文件到上传区域
- ✅ **图片裁剪**：内置图片裁剪功能
- ✅ **粘贴上传**：支持 Ctrl+V 粘贴图片
- ✅ **文件预览**：支持图片预览和文件下载
- ✅ **状态管理**：准备上传、上传中、上传完成三种状态

## 📦 组件结构

```
UploadFile/
├── index.vue              # 主组件
├── FileCard/
│   └── index.vue          # 文件卡片组件
├── CoverImage/
│   └── index.vue          # 封面图片组件
├── type.ts                # 类型定义
└── README.md              # 说明文档
```

## 🎯 支持的文件类型

| 类型 | 扩展名 | 图标颜色 | 特殊功能 |
|------|--------|----------|----------|
| **Word 文档** | .doc, .docx | 🔵 蓝色 | 自动下载 |
| **Excel 表格** | .xls, .xlsx | 🟢 绿色 | 自动下载 |
| **PDF 文档** | .pdf | 🔴 红色 | 新窗口预览 |
| **PowerPoint** | .ppt, .pptx | 🟠 橙色 | 自动下载 |
| **压缩文件** | .zip, .rar, .7z 等 | 🟣 紫色 | 自动下载 |
| **图片文件** | .jpg, .png, .gif 等 | - | 预览放大 |
| **其他文件** | 其他格式 | ⚫ 灰色 | 根据类型处理 |

## 📊 状态流转

```mermaid
graph LR
    A[文件选择] --> B[准备上传状态]
    B --> C[开始上传]
    C --> D[上传中状态]
    D --> E[上传完成]
    E --> F[正常显示]

    B -.-> G[取消/失败]
    D -.-> G
    G --> H[移除文件]
```

### 状态说明

| 状态 | 图标 | 描述 | 用户操作 |
|------|------|------|----------|
| **准备上传** | 🕐 时钟 | 文件已选择，等待上传开始 | 可以取消 |
| **上传中** | 🔄 旋转loading | 文件正在上传，显示进度条 | 等待完成 |
| **上传完成** | 📁 文件图标 | 上传成功，可以预览下载 | 预览/下载/删除 |

## 📝 Props 参数

### UploadFile 组件

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `fileList` | `string[]` | `[]` | 文件URL列表 |
| `multiple` | `boolean` | `false` | 是否支持多文件上传 |
| `dragable` | `boolean` | `true` | 是否支持拖拽上传 |
| `accept` | `string` | `'*'` | 接受的文件类型 |
| `autoUpload` | `boolean` | `true` | 是否自动上传 |
| `imageShown` | `boolean` | `true` | 是否显示已上传的文件 |
| `showSubmitBtn` | `boolean` | `false` | 是否显示提交按钮 |
| `paste` | `boolean` | `false` | 是否支持粘贴上传 |
| `scene` | `string` | `'product'` | 上传场景值 |
| `type` | `string` | `'product'` | 上传类型 |
| `uploadText` | `string` | `'上传'` | 上传按钮文字 |
| `fileName` | `string` | `''` | 文件名 |
| `additionalText` | `string` | `''` | 额外提示文字 |
| `status` | `UploadWay` | `UploadWay.TEST` | 上传环境（测试/正式） |
| `containerClassName` | `string` | `''` | 容器样式类名 |

### FileCard 组件

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `fileUrl` | `string` | `''` | 文件URL |
| `allUrls` | `string[]` | `[]` | 所有文件URL列表 |
| `clearDisabled` | `boolean` | `false` | 是否禁用清除 |
| `drownDisabled` | `boolean` | `true` | 是否禁用下载 |
| `defaultDisabled` | `boolean` | `false` | 是否禁用默认设置 |
| `defaultStatus` | `boolean` | `false` | 是否为默认文件 |
| `isUploading` | `boolean` | `false` | 是否正在上传 |
| `uploadProgress` | `number` | `0` | 上传进度（0-100） |
| `isTemporary` | `boolean` | `false` | 是否为临时文件 |

## 🎪 Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `change` | `(id: number)` | 文件变化时触发 |
| `update:fileList` | `(fileList: string[])` | 文件列表更新 |
| `onUploadCurrent` | `(currentFile: string)` | 当前文件上传时触发 |
| `onUploadSuccess` | `(fileList: string[])` | 上传成功时触发 |
| `onDefaultImage` | `(fileUrl: string)` | 设置默认图片时触发 |

## 💻 使用示例

### 基础用法

```vue
<script setup>
import { ref } from 'vue'
import UploadFile from '@/components/UploadFile/index.vue'

const fileList = ref([])

function handleUploadSuccess(files) {
  console.log('上传成功:', files)
}
</script>

<template>
  <UploadFile
    v-model:file-list="fileList"
    :multiple="true"
    :auto-upload="true"
    scene="product"
    @on-upload-success="handleUploadSuccess"
  />
</template>
```

### 高级用法

```vue
<script setup>
import { ref } from 'vue'
import UploadFile from '@/components/UploadFile/index.vue'

const uploadRef = ref()
const fileList = ref([])

function handleUploadCurrent(currentFile) {
  console.log('当前上传:', currentFile)
}

function handleUploadSuccess(files) {
  console.log('上传完成:', files)
  fileList.value = files
}

function handleSetDefault(fileUrl) {
  console.log('设置默认图片:', fileUrl)
}

// 手动触发上传
function triggerUpload() {
  uploadRef.value?.uploadFun()
}
</script>

<template>
  <UploadFile
    ref="uploadRef"
    v-model:file-list="fileList"
    :multiple="true"
    :auto-upload="false"
    :image-shown="true"
    :paste="true"
    :show-submit-btn="true"
    scene="product"
    upload-text="批量上传"
    additional-text="支持 jpg、png、pdf、doc 等格式"
    @on-upload-current="handleUploadCurrent"
    @on-upload-success="handleUploadSuccess"
    @on-default-image="handleSetDefault"
  />
</template>
```

### 图片裁剪功能

```vue
<script setup>
import { ref } from 'vue'

const uploadRef = ref()
const imageList = ref([])

function showCropper() {
  uploadRef.value?.triggerShowCropper(true)
}

function handleAvatarUpload(files) {
  console.log('头像上传成功:', files)
}
</script>

<template>
  <UploadFile
    ref="uploadRef"
    v-model:file-list="imageList"
    :auto-upload="false"
    scene="avatar"
    @on-upload-success="handleAvatarUpload"
  />

  <el-button @click="showCropper">
    裁剪图片
  </el-button>
</template>
```

## 🔧 API 参考

### 组件方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `triggerShowCropper` | `(show: boolean)` | `void` | 显示/隐藏图片裁剪器 |
| `handleHttpRequest` | `(options: UploadRequestOptions)` | `Promise<void>` | 处理上传请求 |
| `uploadFun` | `(tempUrl?: string)` | `Promise<void>` | 执行文件上传 |

### 类型定义

```typescript
// 上传环境枚举
enum UploadWay {
  TEST = 1,    // 测试环境
  PROD = 2     // 正式环境
}

// 文件列表缓存类型
interface FileListCache {
  url: string
  file: File
}

// 上传进度类型
interface UploadProgress {
  isUploading: boolean
  progress: number
}
```

### 工具函数

```typescript
// 上传文件到CDN
uploadCDNImg(
  file: File,
  scene: string = 'product',
  name: string = '',
  status: UploadWay = UploadWay.TEST,
  onProgress?: (progress: number) => void
): Promise<any>

// 获取文件类型
getFileType(name: string): 'image' | 'video' | 'office' | false

// 判断是否为PDF文件
fileIsPdf(name: string): 'office' | false
```

## ⚠️ 注意事项

### 文件大小限制
- 建议单个文件不超过 10MB
- 图片文件建议不超过 5MB
- 可通过后端配置调整限制

### 文件类型限制
```javascript
// 支持的图片类型
const imgTypes = ['gif', 'jpeg', 'jpg', 'bmp', 'png', 'jfif']

// 支持的视频类型
const videoTypes = ['avi', 'wmv', 'mkv', 'mp4', 'mov', 'rm', '3gp', 'flv', 'mpg', 'rmvb', 'quicktime']

// 支持的办公文档类型
const officeTypes = ['xls', 'xlsx', 'doc', 'docx', 'pdf', 'ppt', 'pptx']

// 支持的压缩文件类型
const archiveTypes = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'tar.gz', 'tar.bz2', 'tar.xz']
```

### 环境配置
- 测试环境：`UploadWay.TEST`
- 正式环境：`UploadWay.PROD`
- 自动根据 `import.meta.env.MODE` 判断环境

### 性能优化建议
1. **大文件上传**：建议使用分片上传
2. **多文件上传**：建议限制并发数量
3. **图片压缩**：上传前可进行客户端压缩
4. **进度监听**：避免频繁更新UI

### 常见问题

**Q: 上传失败怎么办？**
A: 检查网络连接、文件大小、文件类型是否符合要求

**Q: 进度条不显示？**
A: 确保 `autoUpload` 为 `true` 且文件已正确选择

**Q: 图片裁剪功能不工作？**
A: 确保已正确引入 VueCropper 组件

**Q: 文件预览不正常？**
A: 检查文件URL是否正确，CDN配置是否正常
