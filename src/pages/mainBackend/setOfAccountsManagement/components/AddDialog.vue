<script lang="ts" setup>
import { computed, onMounted, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { GetTenantPackageList, ManualAddPayRecord } from '@/api'
import { formatPriceMul } from '@/common/format'
import UploadFile from '@/components/UploadFile/index.vue'

const emits = defineEmits(['handleSubmit'])

const { fetchData: getTenantPackageList, data: tenantPackageList } = GetTenantPackageList()

const { fetchData: handAddFun, success: addSuccess, msg: addMessage, loading: addLoading } = ManualAddPayRecord()

onMounted(async () => {
  await getTenantPackageList()
})

const state = reactive({
  showModal: false,
  modalName: '添加购买记录',
  form: {
    tenant_management_id: '',
    // 商户订单号
    trade_no: '',
    // 微信支付订单号
    pay_record_order_no: '',
    // 支付者昵称
    payer_name: '',
    // 支付时间
    pay_time: '',
    // 支付金额
    pay_price: '',
    // 支付内容
    tenant_package_id: '',
    // 服务天数
    start_time: '',
    // 支付凭证
    pay_voucher_url: '',
  },
})
// const ruleFormRef = ref()
//
// function handCancel() {
//   state.showModal = false
// }

defineExpose({
  state,
})

async function handUpload(url: any) {
  state.form.pay_voucher_url = url[0]
}

// async function handleUploadFile(rawFile: File) {
//   return new Promise<{ flag: boolean, msg?: string, res?: any }>(async (resolve, reject) => {
//     if (rawFile) {
//       const res: any = await uploadCDNImg(rawFile)
//
//       if (res?.code == 200)
//         resolve({ flag: true, res })
//       else
//         reject({ flag: false, msg: '上传失败' })
//     }
//     else {
//       reject({ flag: false, msg: '上传失败' })
//     }
//   })
// }

async function handleSubmit() {
  if (state.form.pay_price === '') {
    ElMessage.error('请输入支付金额')
    return
  }
  if (state.form.tenant_package_id === '') {
    ElMessage.error('请选择支付内容')
    return
  }
  if (state.form.start_time === '') {
    ElMessage.error('请选择服务天数')
    return
  }

  const query = {
    tenant_management_id: Number(state.form.tenant_management_id),
    trade_no: state.form.trade_no,
    pay_record_order_no: state.form.pay_record_order_no,
    payer_name: state.form.payer_name,
    pay_time: state.form.pay_time,
    pay_price: formatPriceMul(Number(state.form.pay_price)),
    tenant_package_id: state.form.tenant_package_id,
    start_time: state.form.start_time,
    pay_voucher_url: state.form.pay_voucher_url,
  }

  await handAddFun(query)

  if (!addSuccess.value) {
    ElMessage.error(addMessage.value)
    return
  }

  emits('handleSubmit')

  state.showModal = false
  state.form = {
    trade_no: '',
    pay_record_order_no: '',
    payer_name: '',
    pay_time: '',
    pay_price: '',
    tenant_package_id: '',
    start_time: '',
    pay_voucher_url: '',
  }
}

const finalValidityPeriod = computed(() => {
  const period_of_validity_days = tenantPackageList.value.list.find((item: any) => item.id === state.form.tenant_package_id)?.period_of_validity_days
  if (period_of_validity_days && state.form.start_time) {
    const endDate = new Date(new Date(state.form.start_time).getTime() + period_of_validity_days * 24 * 60 * 60 * 1000)

    const year = endDate.getFullYear()
    const month = (endDate.getMonth() + 1).toString().padStart(2, '0') // 月从0开始，加1，并确保两位数字
    const day = endDate.getDate().toString().padStart(2, '0') // 确保天是两位数字

    return `${year}-${month}-${day}` // 使用模板字符串格式化日期
  }
  return ''
})
</script>

<template>
  <vxe-modal v-model="state.showModal" show-footer :title="state.modalName" width="680" :mask="false" :lock-view="false" :esc-closable="true" resize>
    <div class="flex flex-wrap gap-8">
      <div class="flex items-center w-[17.5rem]">
        <div class="w-24">
          商户订单号
        </div>
        <div><el-input v-model="state.form.trade_no" clearable /></div>
      </div>
      <div class="flex justify-between items-center w-[19rem]">
        <div class="w-28">
          微信支付订单号
        </div>
        <div><el-input v-model="state.form.pay_record_order_no" clearable /></div>
      </div>
      <div class="flex justify-between items-center w-[17.5rem]">
        <div class="w-24">
          支付者昵称
        </div>
        <div><el-input v-model="state.form.payer_name" clearable /></div>
      </div>
      <div class="flex justify-between items-center w-[17.5rem]">
        <div class="w-24">
          支付时间
        </div>
        <el-date-picker v-model="state.form.pay_time" type="date" format="YYYY/MM/DD" value-format="YYYY-MM-DD" placeholder="支付时间" />
      </div>
      <div class="flex justify-between items-center w-[17.5rem]">
        <div class="w-[5rem]">
          <span class="text-red-600">*</span>
          支付金额
        </div>
        <div><el-input v-model="state.form.pay_price" clearable type="number" /></div>
      </div>
      <div class="flex justify-between items-center w-[19rem]">
        <div class="w-24">
          <span class="text-red-600">*</span>
          支付内容
        </div>
        <div>
          <el-select v-model="state.form.tenant_package_id" class="m-2" placeholder="购买服务的内容" clearable>
            <el-option v-for="item in tenantPackageList.list" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </div>
      </div>
      <div class="flex items-center w-[19rem]">
        <div class="w-24">
          <span class="text-red-600">*</span>
          服务天数
        </div>
        <div>
          <el-date-picker v-model="state.form.start_time" format="YYYY/MM/DD" value-format="YYYY-MM-DD" type="date" />
        </div>
      </div>
      <div class="flex items-center w-[14rem]">
        <div>至: {{ finalValidityPeriod }}</div>
      </div>
      <div class="flex items-center w-[20.5rem]">
        <div class="w-24">
          <span class="text-red-600">*</span>
          支付凭证
        </div>
        <UploadFile multiple :show-submit-btn="false" @on-upload-success="handUpload" />
      </div>
    </div>
    <template #footer>
      <!--      <el-button @click="handCancel">取消</el-button> -->
      <el-button type="primary" :loading="addLoading" @click="handleSubmit">
        确认
      </el-button>
    </template>
  </vxe-modal>
</template>
