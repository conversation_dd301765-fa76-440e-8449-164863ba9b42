<script lang="ts" setup name="DeveloperResourcesUpload">
import { reactive, ref, watch } from 'vue'
import { ElForm, ElMessage } from 'element-plus'
import { breakupUrl } from '@/common/util'
import UploadFile from '@/components/UploadFile/index.vue'
import FildCard from '@/components/FildCard.vue'
import { UploadWay } from '@/common/uploadImage'
import { GET_IMG_CND_Prefix } from '@/common/constant'

const formData = reactive({
  texture_url: [] as string[],
  test_url: '',
  url: '',
  status: 1,
  base_url: '',
  scene: 'product',
  show_url: '',
  prev_view_url: '',
})

const formRef = ref<InstanceType<typeof ElForm> | null>(null)
const uploadFileRef = ref()

const rules = ref({
  texture_url: [
    {
      required: true,
      validator: (rule: any, value: any, callback: any) => {
        if (!uploadFileRef.value.fileInfo)
          callback(new Error('请上传资源'))
        else
          callback()
      },
      trigger: 'change',
    },
  ],
  scene: [
    {
      required: true,
      message: '请输入场景值',
      trigger: 'change',
    },
  ],
})

function handleUpload(list: string[]) {
  ElMessage({
    type: 'success',
    message: '上传成功',
  })
  formData.texture_url = list
  if (formData.status === UploadWay.TEST)
    formData.test_url = GET_IMG_CND_Prefix('test') + breakupUrl(list[0])
  else
    formData.url = GET_IMG_CND_Prefix('production') + breakupUrl(list[0])
}

async function handleShowUpload(num: UploadWay) {
  if (!formRef.value)
    return
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      formData.status = num
      uploadFileRef.value.uploadFun()
    }
    else {
      ElMessage({
        type: 'error',
        message: '请选择资源',
      })
    }
  })
}

watch(
  () => formData.prev_view_url,
  () => {
    if (formData.status === 1)
      formData.test_url = formData.prev_view_url
    else
      formData.url = formData.prev_view_url
  },
)
</script>

<template>
  <FildCard title="上传资源到CDN" :tool-bar="false">
    <template #right-top>
      <el-button type="primary" @click="handleShowUpload(UploadWay.TEST)">
        上传测试
      </el-button>
      <el-button type="primary" @click="handleShowUpload(UploadWay.PROD)">
        上传正式
      </el-button>
    </template>
    <ElForm ref="formRef" label-position="left" :rules="rules" label-width="100px" :model="formData" style="max-width: 460px">
      <el-form-item label="资源" prop="texture_url">
        <div class="image-content">
          <UploadFile
            ref="uploadFileRef"
            image-shown
            :scene="formData.scene"
            :status="formData.status"
            :auto-upload="false"
            :multiple="false"
            :show-submit-btn="false"
            :file-list="formData.texture_url"
            @on-upload-success="handleUpload"
          />
        </div>
      </el-form-item>
      <el-form-item label="场景值" prop="scene">
        <el-input v-model.trim="formData.scene" />
      </el-form-item>
      <el-form-item label="测试环境" prop="test_url">
        <el-input v-model.trim="formData.test_url" readonly />
      </el-form-item>
      <el-form-item label="正式环境" prop="url">
        <el-input v-model.trim="formData.url" readonly />
      </el-form-item>
    </ElForm>
  </FildCard>
</template>

<style lang="scss" scoped>
.image-content {
  display: flex;
}

.image-content button {
  margin-left: 10px;
}

.tips {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
}

.jump-target {
  margin-top: 10px;
}

.other {
  border: 1px solid #eee;
  padding: 10px;
}
</style>
