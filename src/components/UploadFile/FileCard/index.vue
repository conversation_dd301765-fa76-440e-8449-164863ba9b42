<script lang="ts" setup>
import { Clock, Document, Loading, Select } from '@element-plus/icons-vue'

import { fileIsPdf, getFileType } from '@/common/uploadImage'
import { formatFileName, getFileName } from '@/common/util'

interface ParamType {
  fileUrl: string
  allUrls?: any
  clearDisabled: boolean
  drownDisabled: boolean
  defaultDisabled: boolean
  defaultStatus: boolean
  // 上传进度相关
  isUploading?: boolean
  uploadProgress?: number
  // 是否为临时文件（准备上传状态）
  isTemporary?: boolean
}
const props = withDefaults(defineProps<ParamType>(), {
  fileUrl: '',
  allUrls: [],
  clearDisabled: false,
  drownDisabled: true,
  defaultDisabled: false,
  defaultStatus: false,
  isUploading: false,
  uploadProgress: 0,
  isTemporary: false,
})

// 获取文件扩展名
function getFileExtension(filename: string): string {
  if (!filename)
    return ''

  const lowerFilename = filename.toLowerCase()

  // 处理复合扩展名
  if (lowerFilename.endsWith('.tar.gz'))
    return 'tar.gz'
  if (lowerFilename.endsWith('.tar.bz2'))
    return 'tar.bz2'
  if (lowerFilename.endsWith('.tar.xz'))
    return 'tar.xz'

  // 处理普通扩展名
  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex === -1)
    return ''
  return filename.slice(lastDotIndex + 1).toLowerCase()
}

// 判断文件是否需要下载
function shouldDownload(filename: string): boolean {
  const extension = getFileExtension(filename)
  // 压缩文件和其他非预览文件应该下载
  const downloadExtensions = ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'tar.gz', 'tar.bz2', 'tar.xz', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx']
  return downloadExtensions.includes(extension)
}

// 获取显示的文件名（处理临时URL）
function getDisplayFileName(fileUrl: string): string {
  if (fileUrl.startsWith('temp_')) {
    // 从临时URL中提取文件名：temp_timestamp_filename
    const parts = fileUrl.split('_')
    return parts.slice(2).join('_') // 去掉 temp_ 和 timestamp_
  }
  return getFileName(fileUrl)
}

// 获取文件图标和颜色
function getFileIcon(filename: string) {
  // 如果是临时URL，从中提取文件名
  const actualFilename = filename.startsWith('temp_') ? getDisplayFileName(filename) : filename
  const extension = getFileExtension(actualFilename)

  // Word 文档
  if (['doc', 'docx'].includes(extension)) {
    return {
      icon: 'word',
      color: '#2B579A',
      bgColor: '#E7F3FF',
    }
  }

  // Excel 文档
  if (['xls', 'xlsx'].includes(extension)) {
    return {
      icon: 'excel',
      color: '#217346',
      bgColor: '#E8F5E8',
    }
  }

  // PDF 文档
  if (['pdf'].includes(extension)) {
    return {
      icon: 'pdf',
      color: '#DC3545',
      bgColor: '#FFE6E6',
    }
  }

  // PowerPoint 文档
  if (['ppt', 'pptx'].includes(extension)) {
    return {
      icon: 'powerpoint',
      color: '#D24726',
      bgColor: '#FFF2E6',
    }
  }

  // 压缩文件
  if (['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz', 'tar.gz', 'tar.bz2', 'tar.xz'].includes(extension)) {
    return {
      icon: 'archive',
      color: '#8B5CF6',
      bgColor: '#F3E8FF',
    }
  }

  // 默认文件图标
  return {
    icon: 'file',
    color: '#6C757D',
    bgColor: '#F8F9FA',
  }
}
</script>

<template>
  <div class="fileCard">
    <div v-if="props.defaultStatus" class="defaultStatus">
      <el-icon><Select /></el-icon>
    </div>

    <!-- 上传进度覆盖层 -->
    <div v-if="props.isUploading" class="upload-overlay">
      <div class="upload-progress-container">
        <div class="upload-icon">
          <el-icon class="rotating">
            <Loading />
          </el-icon>
        </div>
        <div class="progress-info">
          <div class="progress-text">
            上传中...
          </div>
          <el-progress
            :percentage="props.uploadProgress"
            :stroke-width="4"
            :show-text="false"
            color="#409eff"
          />
          <div class="progress-percentage">
            {{ props.uploadProgress }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 准备上传状态覆盖层 -->
    <div v-else-if="props.isTemporary" class="prepare-overlay">
      <div class="prepare-container">
        <div class="prepare-icon">
          <el-icon>
            <Clock />
          </el-icon>
        </div>
        <div class="prepare-text">
          准备上传...
        </div>
      </div>
    </div>
    <div v-if="getFileType(props?.fileUrl) !== 'image'" class="file-thumbnail item-thumbnail" :style="{ backgroundColor: getFileIcon(props?.fileUrl).bgColor }">
      <div class="file-icon-container">
        <!-- Word 图标 -->
        <div v-if="getFileIcon(props?.fileUrl).icon === 'word'" class="file-icon" :style="{ color: getFileIcon(props?.fileUrl).color }">
          <svg viewBox="0 0 24 24" width="32" height="32" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            <path d="M15,13H16.5V15H15V13M10.5,13H12V15H10.5V13M13,13H14.5V15H13V13M8,13H9.5V15H8V13M15,10.5H16.5V12.5H15V10.5M10.5,10.5H12V12.5H10.5V10.5M13,10.5H14.5V12.5H13V10.5M8,10.5H9.5V12.5H8V10.5Z" />
          </svg>
        </div>

        <!-- Excel 图标 -->
        <div v-else-if="getFileIcon(props?.fileUrl).icon === 'excel'" class="file-icon" :style="{ color: getFileIcon(props?.fileUrl).color }">
          <svg viewBox="0 0 24 24" width="32" height="32" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            <path d="M11.5,16.5L13,13L11.5,9.5L10,13L11.5,16.5M14.5,9.5L16,13L14.5,16.5L13,13L14.5,9.5M9.5,9.5L11,13L9.5,16.5L8,13L9.5,9.5Z" />
          </svg>
        </div>

        <!-- PDF 图标 -->
        <div v-else-if="getFileIcon(props?.fileUrl).icon === 'pdf'" class="file-icon" :style="{ color: getFileIcon(props?.fileUrl).color }">
          <svg viewBox="0 0 24 24" width="32" height="32" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            <path d="M10.5,11H12.5C13.3,11 14,11.7 14,12.5V13.5C14,14.3 13.3,15 12.5,15H11V16.5H10.5V11M11,14.5H12.5C13,14.5 13.5,14 13.5,13.5V12.5C13.5,12 13,11.5 12.5,11.5H11V14.5Z" />
          </svg>
        </div>

        <!-- PowerPoint 图标 -->
        <div v-else-if="getFileIcon(props?.fileUrl).icon === 'powerpoint'" class="file-icon" :style="{ color: getFileIcon(props?.fileUrl).color }">
          <svg viewBox="0 0 24 24" width="32" height="32" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            <path d="M10.5,11H12.5C13.3,11 14,11.7 14,12.5C14,13.3 13.3,14 12.5,14H11V16.5H10.5V11M11,13.5H12.5C13,13.5 13.5,13 13.5,12.5C13.5,12 13,11.5 12.5,11.5H11V13.5Z" />
          </svg>
        </div>

        <!-- 压缩文件图标 -->
        <div v-else-if="getFileIcon(props?.fileUrl).icon === 'archive'" class="file-icon" :style="{ color: getFileIcon(props?.fileUrl).color }">
          <svg viewBox="0 0 24 24" width="32" height="32" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
            <path d="M9.5,10.5H10.5V11.5H9.5V10.5M10.5,11.5H11.5V12.5H10.5V11.5M9.5,12.5H10.5V13.5H9.5V12.5M10.5,13.5H11.5V14.5H10.5V13.5M9.5,14.5H10.5V15.5H9.5V14.5M10.5,15.5H11.5V16.5H10.5V15.5M11.5,10.5H12.5V11.5H11.5V10.5M12.5,11.5H13.5V12.5H12.5V11.5M11.5,12.5H12.5V13.5H11.5V12.5M12.5,13.5H13.5V14.5H12.5V13.5M11.5,14.5H12.5V15.5H11.5V14.5M12.5,15.5H13.5V16.5H12.5V15.5Z" />
          </svg>
        </div>

        <!-- 默认文件图标 -->
        <div v-else class="file-icon" :style="{ color: getFileIcon(props?.fileUrl).color }">
          <el-icon size="32">
            <Document />
          </el-icon>
        </div>
      </div>

      <div class="file-name">
        <el-link
          v-if="!props.fileUrl.startsWith('temp_')"
          type="primary"
          :href="formatFileName(props?.fileUrl)"
          :target="fileIsPdf(props.fileUrl) ? '_blank' : '_self'"
          :download="shouldDownload(props?.fileUrl) ? getDisplayFileName(props?.fileUrl) : undefined"
        >
          {{ getDisplayFileName(props?.fileUrl) }}
        </el-link>
        <span v-else class="temp-file-name">
          {{ getDisplayFileName(props?.fileUrl) }}
        </span>
      </div>
    </div>
    <el-image v-else class="item-thumbnail w-full h-full rounded-lg" fit="cover" alt="Preview Image" :preview-src-list="[props?.fileUrl]" :src="props?.fileUrl" />
  </div>
</template>

<style lang="scss" scoped>
.fileCard {
  width: 100px;
  height: 100px;
  position: relative;
  border: 1px solid #dcdfe6;
  margin: 0 5px;
  display: inline-block;
  overflow: hidden;
  border-radius: 5px;
  .item-thumbnail {
    width: 100px;
    height: 100px;
    border-radius: 5px;
  }

  .file-thumbnail {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px;

    .file-icon-container {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;

      .file-icon {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .file-name {
      text-align: center;
      font-size: 12px;
      line-height: 1.2;
      max-width: 100%;
      overflow: hidden;

      .el-link {
        font-size: 12px;
        line-height: 1.2;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }

      .temp-file-name {
        font-size: 12px;
        line-height: 1.2;
        color: #666;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
      }
    }
  }
  .item-actions {
    opacity: 0;
    position: absolute;
    margin: auto;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    padding: 0 5px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    &:hover {
      opacity: 1;
    }
  }
  .item-button {
    display: block;
    // padding: 10px;
    color: #fff;
    font-size: 20px;
    cursor: pointer;
  }
  .defaultStatus {
    position: absolute;
    line-height: inherit;
    justify-content: center;
    align-items: center;
    right: -15px;
    top: -6px;
    width: 40px;
    height: 24px;
    background: #67c23a;
    text-align: center;
    transform: rotate(45deg);
    color: #fff;
    font-size: 12px;
    i {
      margin-top: 10px;
      transform: rotate(-45deg);
    }
  }

  // 上传进度覆盖层
  .upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;

    .upload-progress-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 80%;

      .upload-icon {

        .rotating {
          font-size: 24px;
          color: #409eff;
          animation: rotate 1s linear infinite;
        }
      }

      .progress-info {
        width: 100%;
        text-align: center;

        .progress-text {
          font-size: 12px;
          color: #666;
          margin-bottom: 4px;
        }

        .progress-percentage {
          font-size: 11px;
          color: #409eff;
          margin-top: 4px;
          font-weight: 500;
        }
      }
    }
  }

  // 准备上传覆盖层
  .prepare-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;

    .prepare-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .prepare-icon {
        margin-bottom: 8px;

        .el-icon {
          font-size: 20px;
          color: #909399;
        }
      }

      .prepare-text {
        font-size: 12px;
        color: #909399;
        text-align: center;
      }
    }
  }

  // 旋转动画
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
</style>
