<script setup lang="ts">
import type { UploadInstance, UploadRawFile, UploadRequestOptions } from 'element-plus/es/components/upload'
import { ElMessage } from 'element-plus'
import { reactive, ref, toRaw, toRefs, unref, watch } from 'vue'
import FileCard from './FileCard/index.vue'
import type { FileListCache } from './type'
import { breakupUrl, formatUrl, getImageFileFromUrl, isProd } from '@/common/util'
import uploadCDNImg, { UploadWay } from '@/common/uploadImage'

interface UploadFilePropsType {
  dragable: boolean
  multiple: boolean
  accept: string
  scene: string
  fileName: string // 文件名称
  type: string
  uploadText: string
  paste?: boolean // 复制黏贴上传功能
  showSubmitBtn: boolean
  fileList: string[]
  containerClassName?: string
  onlyOne?: boolean // 是否只允许上传单张
  defaultImage?: string // 默认图片
  autoUpload: boolean // 是否默认上传
  imageShown?: boolean // 是否显示图片
  status: UploadWay
  additionalText?: string // 额外提示文字
}
const props = withDefaults(defineProps<UploadFilePropsType>(), {
  dragable: true,
  multiple: false,
  // https://developer.mozilla.org/zh-CN/docs/Web/HTML/Element/Input/file#attr-accept
  accept: '*',
  scene: 'product',
  fileName: '',
  type: 'product',
  uploadText: '全部上传',
  // 开启复制黏贴上传功能
  paste: false,
  showSubmitBtn: false,
  /**
   * 格式：
   * ['https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100', 'https://fuss10.elemecdn.com/3/63/4e7f3a15429bfda99bce42a18cdd1jpeg.jpeg?imageMogr2/thumbnail/360x360/format/webp/quality/100']
   */
  fileList: () => [],
  containerClassName: '',
  onlyOne: false,
  autoUpload: true,
  imageShown: true,
  additionalText: '', // 额外提示文字
  status: ['kdyb', 'pre'].includes(import.meta.env.MODE) ? UploadWay.PROD : UploadWay.TEST,
})

const emit = defineEmits<{
  (e: 'change', id: number): void
  (e: 'update:fileList', fileList: string[]): void
  (e: 'onUploadCurrent', currentFile: string): void
  (e: 'onUploadSuccess', fileList: any): void
  (e: 'onDefaultImage', fileList: string): void
}>()

const cropperOptions = reactive({
  img: '',
  size: 1,
  full: false,
  outputType: 'png',
  canMove: true,
  original: false,
  canMoveBox: true,
  autoCrop: true,
  // 只有自动截图开启 宽度高度才生效
  autoCropWidth: 3000,
  autoCropHeight: 3000,
  high: true,
  max: 99999,
})

const fileListCache = ref<FileListCache[]>([])

const uploadRef = ref<UploadInstance>()
const currentFile = ref('')
const newFileList = ref<string[]>([])
const showCropper = ref(false)
// computed(() => {
//   return props.accept === '*' ? '所有文件' : props.accept.replace(',', ' ')
// })
watch(
  () => props.fileList,
  () => {
    console.log('fileList', props.fileList)
    newFileList.value = props?.fileList ? props?.fileList?.map(v => unref(v)) : []
  },
  {
    immediate: true,
  },
)

// eslint-disable-next-line unused-imports/no-unused-vars
const { multiple, dragable, scene, type, uploadText, fileName } = toRefs(props)

// 全部上传
function handleSubmit() {
  if (newFileList.value.length === 0) {
    return ElMessage({
      type: 'error',
      message: '没有文件可以上传',
    })
  }
  const initialList: string[] = []
  newFileList.value.forEach((item) => {
    initialList.push(breakupUrl(item))
  })
  emit('onUploadSuccess', initialList)
}
// 文件信息存储
const fileInfo = ref()

// 上传进度管理
const uploadProgress = ref<Record<string, { isUploading: boolean, progress: number }>>({})

// 根据文件URL获取进度信息
function getFileProgress(fileUrl: string) {
  // 从缓存中查找对应的文件
  const cachedFile = fileListCache.value.find(item => item.url === fileUrl)
  if (cachedFile?.file) {
    const fileKey = cachedFile.file.name + cachedFile.file.size
    const progress = uploadProgress.value[fileKey]
    if (progress)
      return progress

    // 如果是临时URL但没有进度记录，说明还未开始上传
    if (fileUrl.startsWith('temp_'))
      return { isUploading: false, progress: 0 }
  }
  return { isUploading: false, progress: 100 }
}

// 将 File 转换为 UploadRawFile
function createUploadRawFile(file: File): UploadRawFile {
  const uploadFile = file as UploadRawFile
  uploadFile.uid = Date.now() + Math.random()
  return uploadFile
}

// 自定义上传请求处理
function handleHttpRequest(options: UploadRequestOptions) {
  console.log('handleHttpRequest called', options)

  // 裁剪完成之后再次调用 handleHttpRequest
  if (showCropper.value && !cropperOptions.img) {
    handleImageCut(options.file)
    return Promise.resolve()
  }

  // 保存文件信息
  fileInfo.value = options.file
  console.log('fileInfo', fileInfo.value)

  // 立即添加一个临时URL到列表中用于显示进度
  const tempUrl = `temp_${Date.now()}_${options.file.name}`
  if (props.multiple)
    newFileList.value = [...newFileList.value, tempUrl]
  else
    newFileList.value = [tempUrl]

  // 添加到缓存中，用于进度追踪
  setFileListCache(tempUrl, options.file)

  // 当 autoUpload 为 true 时，执行上传
  if (props.autoUpload) {
    ElMessage({
      type: 'info',
      message: '正在解析中...',
    })
    uploadFun(tempUrl)
  }

  // 返回一个 Promise 来阻止 Element Plus 的默认上传行为
  return Promise.resolve()
}

// 请求上传方法
function uploadFun(tempUrl?: string) {
  const rawFile: UploadRawFile = fileInfo.value
  // eslint-disable-next-line no-async-promise-executor
  return new Promise<void>(async (resolve, reject) => {
    const fileread = new FileReader() // 创建文件读取对象
    fileread.readAsDataURL(rawFile) // 文件读取装换为base64类型
    fileread.onloadend = async function () {
      try {
        const successRes: any = await handleUploadFile(rawFile)
        if (successRes.flag) {
          const resUrl = successRes?.res.url
          currentFile.value = formatUrl(resUrl, '')

          // 如果有临时URL，替换它；否则添加新的
          if (tempUrl) {
            const tempIndex = newFileList.value.findIndex(item => item === tempUrl)
            if (tempIndex !== -1)
              newFileList.value[tempIndex] = currentFile.value

            // 更新缓存
            setFileListCache(currentFile.value, rawFile)
            // 删除临时缓存
            const tempCacheIndex = fileListCache.value.findIndex(item => item.url === tempUrl)
            if (tempCacheIndex !== -1)
              fileListCache.value.splice(tempCacheIndex, 1)
          }
          else {
            newFileList.value = props.multiple ? [...new Set([...newFileList.value, currentFile.value])] : [currentFile.value]
            setFileListCache(currentFile.value, rawFile)
          }

          console.log('newFileList', newFileList)
          emit('onUploadCurrent', unref(currentFile))
          emit('update:fileList', toRaw(newFileList.value))
          emit('onUploadSuccess', newFileList.value)
          fileInfo.value = ''
          if (props.autoUpload)
            ElMessage.success('上传成功')
        }
      }
      catch (e) {
        // 上传失败时，移除临时URL
        if (tempUrl) {
          const tempIndex = newFileList.value.findIndex(item => item === tempUrl)
          if (tempIndex !== -1)
            newFileList.value.splice(tempIndex, 1)

          // 删除临时缓存
          const tempCacheIndex = fileListCache.value.findIndex(item => item.url === tempUrl)
          if (tempCacheIndex !== -1)
            fileListCache.value.splice(tempCacheIndex, 1)
        }
        reject(e)
      }
    }
  })
}

function setFileListCache(url: string, rawFile: File) {
  const index = fileListCache.value.findIndex(item => item.url === url)
  if (index === -1) {
    fileListCache.value.push({
      url,
      file: rawFile,
    })
  }
  else {
    fileListCache.value[index] = {
      url,
      file: rawFile,
    }
  }
}
// 预览
function onPreview() {}
// 删除
function onRemove(url: string) {
  const index = newFileList.value.findIndex(item => item === url)
  newFileList.value.splice(index, 1)

  emit('update:fileList', newFileList.value)
  emit('onUploadSuccess', newFileList.value)
}

async function handleUploadFile(rawFile: File) {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise<{ flag: boolean, msg?: string, res?: any }>(async (resolve, reject) => {
    if (rawFile) {
      const fileKey = rawFile.name + rawFile.size // 创建唯一文件标识

      // 初始化上传状态
      uploadProgress.value[fileKey] = { isUploading: true, progress: 0 }

      const defaultUploadWay = isProd() ? UploadWay.PROD : UploadWay.TEST

      try {
        const res: any = await uploadCDNImg(
          rawFile,
          unref(scene),
          rawFile.name,
          props.status ?? defaultUploadWay,
          (progress) => {
            // 更新上传进度
            if (uploadProgress.value[fileKey])
              uploadProgress.value[fileKey].progress = progress
            console.log('progress', uploadProgress.value[fileKey].progress)
          },
        )

        // 上传完成，清除进度状态
        if (uploadProgress.value[fileKey]) {
          uploadProgress.value[fileKey].isUploading = false
          uploadProgress.value[fileKey].progress = 100
        }

        if (res?.code === 200) {
          resolve({ flag: true, res })
        }
        else {
          // eslint-disable-next-line prefer-promise-reject-errors
          reject({ flag: false, msg: '上传失败' })
        }
      }
      catch (error) {
        // 上传失败，清除进度状态
        if (uploadProgress.value[fileKey])
          uploadProgress.value[fileKey].isUploading = false

        // eslint-disable-next-line prefer-promise-reject-errors
        reject({ flag: false, msg: '上传失败' })
      }
    }
    else {
      // eslint-disable-next-line prefer-promise-reject-errors
      reject({ flag: false, msg: '上传失败' })
    }
  })
}

function handlePaste(event: ClipboardEvent) {
  const paste = event.clipboardData?.items[0]
  if (!paste || paste.kind !== 'file') {
    return ElMessage({
      type: 'error',
      message: '请粘贴文件',
    })
  }
  const imageFile = paste.getAsFile()
  if (!imageFile) {
    return ElMessage({
      type: 'error',
      message: '获取文件失败',
    })
  }
  handleHttpRequest({
    action: '#',
    method: 'post',
    data: {},
    filename: imageFile.name,
    file: createUploadRawFile(imageFile),
    headers: {},
    onError: () => {},
    onProgress: () => {},
    onSuccess: () => {},
    withCredentials: false,
  })
}

const cropperRef = ref()

const previews = ref<any>({})

function triggerShowCropper(bool: boolean) {
  showCropper.value = bool
}

defineExpose({
  triggerShowCropper,
  handleHttpRequest,
  fileListCache,
  uploadFun,
  fileInfo,
})

function handleImageCut(rowFile: UploadRawFile) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(rowFile)
    reader.onload = (e) => {
      if (e.target) {
        cropperOptions.img = e.target.result as string
        resolve(cropperOptions.img)
      }
    }
    reader.onerror = () => {
      reject(cropperOptions.img)
    }
  })
}

let currentFileBlob: Blob

function onRealTime(data: any) {
  previews.value = data
  cropperRef.value.getCropBlob((data: any) => {
    currentFileBlob = data
  })
}
function onClose() {
  showCropper.value = false
  cropperOptions.img = ''
}
async function handleCutSubmit() {
  //
  let file
  try {
    file = await getImageFileFromUrl(window.URL.createObjectURL(currentFileBlob as Blob), 'cutedImage')
    handleHttpRequest({
      action: '#',
      method: 'post',
      data: {},
      filename: (file as File).name,
      file: createUploadRawFile(file as File),
      headers: {},
      onError: () => {},
      onProgress: () => {},
      onSuccess: () => {},
      withCredentials: false,
    })
  }
  catch (e: any) {
    ElMessage({
      type: 'error',
      message: '导出截取图片失败',
    })
  }
  onClose()
}

function beforeHideMethod() {
  onClose()
  return Promise.resolve()
}

// 默认图片
const defaultImage = ref<string>('')
function onSetDefault(url: string) {
  defaultImage.value = url
  emit('onDefaultImage', url)
}
watch(
  () => props.defaultImage,
  () => {
    defaultImage.value = props.defaultImage || ''
  },
  { immediate: true },
)
</script>

<template>
  <div class="upload-container flex-row" :class="[containerClassName]">
    <div class="upload-controller">
      <el-upload ref="uploadRef" class="flex-row" :show-file-list="false" :http-request="handleHttpRequest" :drag="dragable" :multiple="multiple" :accept="accept" action="#" :auto-upload="true">
        <template #default>
          <el-icon size="70" color="#409eff">
            <UploadFilled />
          </el-icon>
          <div class="el-upload__text text-[14px] mt-[5px]">
            点击或将文件拖拽到这里上传
          </div>
          <div class="el-upload__tip text-gray-400">
            {{ props.additionalText || '' }}
          </div>
        </template>
      </el-upload>
      <el-input v-if="paste" autosize class="w-full mt-2 text-[13px]" type="textarea" placeholder="也可以将文件按Ctrl+V 粘贴至此处" @paste.stop="handlePaste" />
      <div v-if="!props.autoUpload && fileInfo">
        {{ fileInfo?.name }}
      </div>
    </div>
    <template v-if="newFileList?.length > 0 && props.imageShown">
      <div v-for="item in newFileList" :key="item" class="inline-block">
        <FileCard
          :default-status="item === defaultImage"
          width="300px"
          height="300px"
          :file-url="item"
          :all-urls="newFileList"
          :is-uploading="getFileProgress(item).isUploading"
          :upload-progress="getFileProgress(item).progress"
          :is-temporary="item.startsWith('temp_') && !getFileProgress(item).isUploading"
          @preview="onPreview"
          @remove="onRemove"
          @set-default="onSetDefault"
        />
      </div>
    </template>
  </div>
  <div v-if="showSubmitBtn" class="upload-footer">
    <el-button type="primary" @click="handleSubmit">
      {{ uploadText }}
    </el-button>
  </div>
  <vxe-modal v-model="showCropper" title="图片裁剪" width="1000" height="800" show-footer :esc-closable="true" :before-hide-method="beforeHideMethod" resize @close="onClose">
    <template #default>
      <div class="cut w-full h-full">
        <VueCropper
          ref="cropperRef"

          :img="cropperOptions.img"
          :output-size="cropperOptions.size"
          :output-type="cropperOptions.outputType"
          :info="true"
          center-box
          :full="cropperOptions.full"
          :can-move="cropperOptions.canMove"
          :can-move-box="cropperOptions.canMoveBox"
          :original="cropperOptions.original"
          :auto-crop="cropperOptions.autoCrop"
          :auto-crop-width="cropperOptions.autoCropWidth"
          :auto-crop-height="cropperOptions.autoCropHeight"
          :high="cropperOptions.high"
          :max-img-size="cropperOptions.max"
          mode="100%"
          @real-time="onRealTime"
        />
      </div>
    </template>
    <template #footer>
      <el-button type="primary" @click="handleCutSubmit">
        裁剪
      </el-button>
    </template>
  </vxe-modal>
</template>

<style lang="scss" scoped>
.file-card {
  width: 100px;
  height: 100px;
}
.upload-container {
  // width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.upload-controller {
  // width: 100%;
  margin-right: 10px;
}
.upload-footer {
  margin: 20px 0;
}
.file-item {
  width: 100px;
  height: 100px;
  position: relative;
  margin: 5px 5px;
  // padding: 20px;
}
:deep(.el-upload-list__item) {
  transition: none !important;
}

:deep(.el-upload-list) {
  margin-left: 10px;
  flex: 1 1 auto;
}
:deep(.el-upload-dragger) {
  padding: 10px 20px;
}
</style>
